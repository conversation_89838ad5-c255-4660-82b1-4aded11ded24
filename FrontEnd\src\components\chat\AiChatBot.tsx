import React, { useState, useRef, useEffect } from 'react';
import { chatService, Message } from '../../utils/chatService';

// واجهة خصائص المكون
interface AiChatBotProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * مكون روبوت الدردشة الذكي لمساعدة الناخبين
 */
const AiChatBot: React.FC<AiChatBotProps> = ({ isOpen, onClose }) => {
  // حالة المحادثة
  const [messages, setMessages] = useState<Message[]>([
    { role: 'assistant', content: 'مرحباً! أنا المساعد الانتخابي الذكي. كيف يمكنني مساعدتك اليوم؟' }
  ]);

  // حالة الرسالة الجديدة
  const [newMessage, setNewMessage] = useState('');

  // حالة التحميل
  const [isLoading, setIsLoading] = useState(false);

  // مرجع لمنطقة المحادثة للتمرير التلقائي
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // التمرير التلقائي إلى أسفل المحادثة عند إضافة رسائل جديدة
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // إرسال رسالة إلى المساعد الذكي
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newMessage.trim()) return;

    // إضافة رسالة المستخدم إلى المحادثة
    const userMessage: Message = { role: 'user', content: newMessage };
    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');

    // تعيين حالة التحميل
    setIsLoading(true);

    try {
      console.log('Sending message to ChatGPT service:', userMessage.content);

      // إضافة تأخير زمني قصير (0.5 ثانية) قبل إرسال الطلب
      await new Promise(resolve => setTimeout(resolve, 500));

      // استخدام ChatGPT API الحقيقي
      const response = await chatService.sendMessage([...messages, userMessage]);
      console.log('Received response from ChatGPT service:', response);

      // إضافة رد المساعد إلى المحادثة
      const assistantMessage: Message = { role: 'assistant', content: response };
      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error in chat component:', error);

      // تحديد رسالة الخطأ المناسبة
      let errorContent = 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى لاحقاً.';

      // إذا كان الخطأ يحتوي على رسالة محددة، استخدمها
      if (error instanceof Error && error.message) {
        errorContent = `عذراً، ${error.message}`;
      }

      // إضافة رسالة الخطأ إلى المحادثة
      const errorMessage: Message = {
        role: 'assistant',
        content: errorContent
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      // إلغاء حالة التحميل
      setIsLoading(false);
    }
  };

  // إذا كان المكون مغلقاً، لا تعرض شيئاً
  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 w-80 md:w-96 bg-white rounded-lg shadow-xl z-50 flex flex-col h-[500px] max-h-[80vh]">
      {/* رأس المكون */}
      <div className="bg-blue-600 text-white p-3 rounded-t-lg flex justify-between items-center">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
          <h3 className="font-bold">المساعد الانتخابي الذكي (ChatGPT)</h3>
        </div>
        <button
          onClick={onClose}
          className="text-white hover:text-gray-200 focus:outline-none"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      {/* منطقة المحادثة */}
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`mb-4 ${message.role === 'user' ? 'text-left' : 'text-right'}`}
          >
            <div
              className={`inline-block p-3 rounded-lg max-w-[80%] ${
                message.role === 'user'
                  ? 'bg-blue-600 text-white rounded-bl-none'
                  : 'bg-gray-200 text-gray-800 rounded-br-none'
              }`}
            >
              {message.content}
            </div>
          </div>
        ))}

        {/* مؤشر التحميل */}
        {isLoading && (
          <div className="text-right mb-4">
            <div className="inline-block p-3 rounded-lg bg-gray-200 text-gray-800 rounded-br-none">
              <div className="flex space-x-2 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce delay-75"></div>
                <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce delay-150"></div>
              </div>
            </div>
          </div>
        )}

        {/* مرجع للتمرير التلقائي */}
        <div ref={messagesEndRef} />
      </div>

      {/* نموذج إرسال الرسائل */}
      <form onSubmit={handleSendMessage} className="p-3 border-t border-gray-200">
        <div className="flex">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="اكتب رسالتك هنا..."
            className="flex-1 p-2 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={isLoading}
          />
          <button
            type="submit"
            className="bg-blue-600 text-white p-2 rounded-l-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            disabled={isLoading || !newMessage.trim()}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 transform rotate-90" viewBox="0 0 20 20" fill="currentColor">
              <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default AiChatBot;
