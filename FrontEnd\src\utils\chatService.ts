// تعريف واجهة الرسالة
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// تعريف واجهة استجابة ChatGPT
interface ChatGPTResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// الإعدادات الافتراضية للنموذج
const DEFAULT_MODEL = 'deepseek-chat';  // نموذج DeepSeek
const DEFAULT_TEMPERATURE = 0.7;
const DEFAULT_MAX_TOKENS = 1000;

// معلومات حول الانتخابات للسياق
const ELECTION_CONTEXT = `
أنت مساعد ذكي للناخبين في نظام التصويت الإلكتروني. مهمتك هي مساعدة الناخبين خلال العملية الانتخابية وتوجيههم.
معلومات مهمة عن النظام:
1. يجب على الناخب التسجيل أولاً قبل التصويت
2. يمكن للناخب التصويت مرة واحدة فقط في كل انتخابات
3. يمكن للناخب عرض المرشحين ومعلوماتهم قبل التصويت
4. نتائج الانتخابات تظهر فقط بعد انتهاء فترة التصويت
5. يجب على الناخب توصيل محفظته الإلكترونية للتصويت

قدم إجابات مختصرة ومفيدة، وكن ودودًا ومهذبًا في تعاملك مع الناخبين.
`;

/**
 * خدمة للتواصل مع ChatGPT API
 */
class ChatService {
  private apiKey: string;
  private apiUrl: string;
  private model: string;
  private temperature: number;
  private maxTokens: number;

  constructor() {
    // استخدام متغيرات البيئة للحصول على مفتاح API
    this.apiKey = import.meta.env.VITE_DEEPSEEK_API_KEY || '';
    // عنوان URL لـ DeepSeek API
    this.apiUrl = 'https://api.deepseek.com/v1/chat/completions';
    this.model = DEFAULT_MODEL;
    this.temperature = DEFAULT_TEMPERATURE;
    this.maxTokens = DEFAULT_MAX_TOKENS;

    // طباعة معلومات التهيئة للتصحيح
    console.log('DeepSeek Service initialized with API key:', this.apiKey ? 'Present' : 'Missing');
    console.log('DeepSeek API URL:', this.apiUrl);
    console.log('DeepSeek Model:', this.model);
  }

  /**
   * إرسال رسالة إلى ChatGPT API والحصول على استجابة
   * @param messages سجل المحادثة
   * @returns وعد بالاستجابة من ChatGPT
   */
  async sendMessage(messages: Message[]): Promise<string> {
    try {
      // التحقق من وجود مفتاح API
      if (!this.apiKey || this.apiKey === '') {
        throw new Error('مفتاح DeepSeek API غير متوفر. يرجى إضافة VITE_DEEPSEEK_API_KEY في ملف .env');
      }

      console.log('Using real DeepSeek API with key:', this.apiKey.substring(0, 5) + '...');

      // إضافة سياق الانتخابات كرسالة نظام في بداية المحادثة
      const messagesWithContext = [
        { role: 'system', content: ELECTION_CONTEXT },
        ...messages
      ];

      // إعداد طلب API
      console.log('Sending request to ChatGPT API...');

      try {
        // إرسال الطلب إلى ChatGPT API
        const response = await fetch(this.apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model: this.model,
            messages: messagesWithContext,
            temperature: this.temperature,
            max_tokens: this.maxTokens
          })
        });

        // التحقق من نجاح الطلب
        if (!response.ok) {
          console.error('API error:', response.status, response.statusText);

          let errorMessage = 'حدث خطأ في الاتصال بخدمة الذكاء الاصطناعي';

          // تخصيص رسائل الخطأ بناءً على رمز الحالة
          switch (response.status) {
            case 401:
              errorMessage = 'مفتاح API غير صحيح أو منتهي الصلاحية';
              break;
            case 429:
              errorMessage = 'تم تجاوز الحد المسموح من الطلبات. يرجى المحاولة لاحقاً';
              break;
            case 500:
              errorMessage = 'خطأ في خادم DeepSeek. يرجى المحاولة لاحقاً';
              break;
            case 503:
              errorMessage = 'خدمة DeepSeek غير متاحة حالياً. يرجى المحاولة لاحقاً';
              break;
            default:
              errorMessage = `خطأ في API: ${response.status} ${response.statusText}`;
          }

          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await response.json();
            console.error('API error details:', errorData);

            // إذا كان هناك رسالة خطأ محددة من API، استخدمها
            if (errorData.error && errorData.error.message) {
              errorMessage += `: ${errorData.error.message}`;
            }
          } catch (e) {
            console.error('Could not parse error response');
          }

          throw new Error(errorMessage);
        }

        // تحويل الاستجابة إلى JSON
        const data = await response.json() as ChatGPTResponse;
        console.log('ChatGPT API response received');

        // التحقق من وجود الاستجابة
        if (data.choices && data.choices.length > 0 && data.choices[0].message) {
          // استخراج محتوى الرسالة من الاستجابة
          return data.choices[0].message.content;
        } else {
          console.error('Invalid API response format:', data);
          throw new Error('تنسيق استجابة API غير صحيح');
        }
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        throw fetchError;
      }
    } catch (error) {
      console.error('Error in chatbot:', error);
      throw error;
    }
  }



  /**
   * تعيين نموذج ChatGPT
   * @param model اسم النموذج
   */
  setModel(model: string): void {
    this.model = model;
  }

  /**
   * تعيين درجة حرارة النموذج (التنوع في الإجابات)
   * @param temperature قيمة بين 0 و 1
   */
  setTemperature(temperature: number): void {
    this.temperature = temperature;
  }

  /**
   * تعيين الحد الأقصى للرموز في الاستجابة
   * @param maxTokens عدد الرموز
   */
  setMaxTokens(maxTokens: number): void {
    this.maxTokens = maxTokens;
  }
}

// تصدير نسخة واحدة من الخدمة (Singleton)
export const chatService = new ChatService();
