// تعريف واجهة الرسالة
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

// تعريف واجهة استجابة ChatGPT
interface ChatGPTResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// الإعدادات الافتراضية للنموذج
const DEFAULT_MODEL = 'gpt-3.5-turbo';  // نموذج ChatGPT
const DEFAULT_TEMPERATURE = 0.7;
const DEFAULT_MAX_TOKENS = 1000;

// معلومات حول الانتخابات للسياق
const ELECTION_CONTEXT = `
أنت مساعد ذكي للناخبين في نظام التصويت الإلكتروني. مهمتك هي مساعدة الناخبين خلال العملية الانتخابية وتوجيههم.
معلومات مهمة عن النظام:
1. يجب على الناخب التسجيل أولاً قبل التصويت
2. يمكن للناخب التصويت مرة واحدة فقط في كل انتخابات
3. يمكن للناخب عرض المرشحين ومعلوماتهم قبل التصويت
4. نتائج الانتخابات تظهر فقط بعد انتهاء فترة التصويت
5. يجب على الناخب توصيل محفظته الإلكترونية للتصويت

قدم إجابات مختصرة ومفيدة، وكن ودودًا ومهذبًا في تعاملك مع الناخبين.
`;

/**
 * خدمة للتواصل مع ChatGPT API
 */
class ChatService {
  private apiKey: string;
  private apiUrl: string;
  private model: string;
  private temperature: number;
  private maxTokens: number;

  constructor() {
    // استخدام متغيرات البيئة للحصول على مفتاح API
    this.apiKey = import.meta.env.VITE_OPENAI_API_KEY || '';
    // عنوان URL لـ ChatGPT API
    this.apiUrl = 'https://api.openai.com/v1/chat/completions';
    this.model = DEFAULT_MODEL;
    this.temperature = DEFAULT_TEMPERATURE;
    this.maxTokens = DEFAULT_MAX_TOKENS;

    // طباعة معلومات التهيئة للتصحيح
    console.log('ChatGPT Service initialized with API key:', this.apiKey ? 'Present' : 'Missing');
    console.log('ChatGPT API URL:', this.apiUrl);
    console.log('ChatGPT Model:', this.model);
  }

  /**
   * إرسال رسالة إلى ChatGPT API والحصول على استجابة
   * @param messages سجل المحادثة
   * @returns وعد بالاستجابة من ChatGPT
   */
  async sendMessage(messages: Message[]): Promise<string> {
    try {
      // التحقق من وجود مفتاح API
      if (!this.apiKey || this.apiKey === import.meta.env.VITE_OPENAI_API_KEY || this.apiKey === '') {
        console.log('No API key provided, using mock response');
        // استخدام استجابة وهمية عندما لا يكون هناك مفتاح API
        return this.getMockResponse(messages);
      }

      console.log('Using real ChatGPT API with key:', this.apiKey.substring(0, 5) + '...');

      // إضافة سياق الانتخابات كرسالة نظام في بداية المحادثة
      const messagesWithContext = [
        { role: 'system', content: ELECTION_CONTEXT },
        ...messages
      ];

      // إعداد طلب API
      console.log('Sending request to ChatGPT API...');

      try {
        // إرسال الطلب إلى ChatGPT API
        const response = await fetch(this.apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          },
          body: JSON.stringify({
            model: this.model,
            messages: messagesWithContext,
            temperature: this.temperature,
            max_tokens: this.maxTokens
          })
        });

        // التحقق من نجاح الطلب
        if (!response.ok) {
          console.error('API error:', response.status, response.statusText);

          try {
            // محاولة قراءة رسالة الخطأ من الاستجابة
            const errorData = await response.json();
            console.error('API error details:', errorData);
          } catch (e) {
            console.error('Could not parse error response');
          }

          return this.getMockResponse(messages);
        }

        // تحويل الاستجابة إلى JSON
        const data = await response.json() as ChatGPTResponse;
        console.log('ChatGPT API response received');

        // التحقق من وجود الاستجابة
        if (data.choices && data.choices.length > 0 && data.choices[0].message) {
          // استخراج محتوى الرسالة من الاستجابة
          return data.choices[0].message.content;
        } else {
          console.error('Invalid API response format:', data);
          return this.getMockResponse(messages);
        }
      } catch (fetchError) {
        console.error('Fetch error:', fetchError);
        return this.getMockResponse(messages);
      }
    } catch (error) {
      console.error('Error in chatbot:', error);
      return this.getMockResponse(messages);
    }
  }

  // وظيفة للحصول على استجابة وهمية
  public getMockResponse(messages: Message[]): string {
    const lastMessage = messages[messages.length - 1];
    const userQuestion = lastMessage.content.toLowerCase();

    // إنشاء استجابة بناءً على سؤال المستخدم
    if (userQuestion.includes('تسجيل') || userQuestion.includes('ناخب')) {
      return 'للتسجيل كناخب، يجب عليك توصيل محفظتك الإلكترونية أولاً ثم النقر على زر "تسجيل كناخب" في صفحة الانتخابات. بعد التسجيل، ستتمكن من المشاركة في الانتخابات المتاحة.';
    } else if (userQuestion.includes('تصويت') || userQuestion.includes('صوت')) {
      return 'للتصويت، يجب عليك أولاً التسجيل كناخب، ثم اختيار الانتخابات التي ترغب في التصويت فيها، واختيار المرشح الذي تريد التصويت له، ثم النقر على زر "تصويت". يمكنك التصويت مرة واحدة فقط في كل انتخابات.';
    } else if (userQuestion.includes('مرشح') || userQuestion.includes('مرشحين')) {
      return 'يمكنك عرض المرشحين ومعلوماتهم من خلال النقر على الانتخابات في صفحة الانتخابات. ستظهر لك قائمة بجميع المرشحين مع صورهم ومعلوماتهم الشخصية وبرامجهم الانتخابية.';
    } else if (userQuestion.includes('نتائج') || userQuestion.includes('نتيجة')) {
      return 'تظهر نتائج الانتخابات فقط بعد انتهاء فترة التصويت. يمكنك عرض النتائج من خلال النقر على زر "النتائج" في صفحة الانتخابات. ستظهر النتائج مرتبة تنازلياً حسب عدد الأصوات.';
    } else if (userQuestion.includes('محفظة') || userQuestion.includes('اتصال')) {
      return 'لتوصيل محفظتك الإلكترونية، انقر على زر "اتصال" في أعلى الصفحة واتبع التعليمات. يمكنك استخدام محافظ مثل MetaMask أو WalletConnect للاتصال بالنظام.';
    } else if (userQuestion.includes('كيف') && userQuestion.includes('عمل')) {
      return 'نظام التصويت الإلكتروني يعمل باستخدام تقنية البلوكتشين لضمان الشفافية والأمان. يتم تخزين جميع البيانات على البلوكتشين بشكل آمن ولا يمكن تغييرها. يمكنك التسجيل كناخب، ثم التصويت للمرشح الذي تختاره، وبعد انتهاء فترة التصويت يمكنك عرض النتائج.';
    } else if (userQuestion.includes('من أنت') || userQuestion.includes('ما هو دورك') || userQuestion.includes('ما دورك')) {
      return 'أنا المساعد الانتخابي الذكي، مهمتي هي مساعدتك خلال العملية الانتخابية والإجابة على أسئلتك المتعلقة بالتصويت والانتخابات. يمكنني توجيهك خلال عملية التسجيل والتصويت وعرض النتائج.';
    } else if (userQuestion.includes('شكرا') || userQuestion.includes('شكراً')) {
      return 'العفو! سعيد بمساعدتك. هل هناك أي شيء آخر يمكنني مساعدتك به؟';
    } else if (userQuestion.includes('مميزات') || userQuestion.includes('ميزات') || userQuestion.includes('فوائد')) {
      return 'نظام التصويت الإلكتروني يتميز بالعديد من المميزات، منها:\n1. الشفافية والأمان باستخدام تقنية البلوكتشين\n2. سهولة التصويت من أي مكان\n3. سرعة ظهور النتائج بعد انتهاء فترة التصويت\n4. إمكانية التحقق من صحة التصويت\n5. تقليل التكاليف والجهد مقارنة بالتصويت التقليدي';
    } else if (userQuestion.includes('أمان') || userQuestion.includes('حماية') || userQuestion.includes('خصوصية')) {
      return 'يوفر نظام التصويت الإلكتروني أعلى درجات الأمان والخصوصية من خلال استخدام تقنية البلوكتشين. يتم تشفير جميع البيانات وتخزينها بشكل آمن، ولا يمكن تغييرها بعد تسجيلها. كما يتم التحقق من هوية الناخبين باستخدام المحافظ الإلكترونية.';
    } else if (userQuestion.includes('مرحبا') || userQuestion.includes('أهلا') || userQuestion.includes('السلام عليكم')) {
      return 'مرحباً بك! أنا المساعد الانتخابي الذكي. كيف يمكنني مساعدتك اليوم؟ يمكنني تقديم معلومات حول التسجيل، التصويت، المرشحين، أو النتائج.';
    } else if (userQuestion.includes('وقت') || userQuestion.includes('موعد') || userQuestion.includes('تاريخ')) {
      return 'يمكنك معرفة مواعيد الانتخابات من خلال صفحة الانتخابات. يتم عرض تاريخ بدء ونهاية كل انتخابات، ويمكنك التصويت فقط خلال الفترة المحددة.';
    } else if (userQuestion.includes('مشكلة') || userQuestion.includes('خطأ') || userQuestion.includes('صعوبة')) {
      return 'إذا كنت تواجه أي مشكلة في استخدام النظام، يمكنك التواصل مع فريق الدعم الفني من خلال صفحة "اتصل بنا". سيقوم فريق الدعم بمساعدتك في حل المشكلة في أقرب وقت ممكن.';
    } else {
      return 'مرحباً! أنا المساعد الانتخابي الذكي. يمكنني مساعدتك في عملية التصويت والإجابة على أسئلتك حول النظام. يمكنك سؤالي عن كيفية التسجيل، التصويت، عرض المرشحين، أو عرض النتائج.';
    }
  }

  /**
   * تعيين نموذج ChatGPT
   * @param model اسم النموذج
   */
  setModel(model: string): void {
    this.model = model;
  }

  /**
   * تعيين درجة حرارة النموذج (التنوع في الإجابات)
   * @param temperature قيمة بين 0 و 1
   */
  setTemperature(temperature: number): void {
    this.temperature = temperature;
  }

  /**
   * تعيين الحد الأقصى للرموز في الاستجابة
   * @param maxTokens عدد الرموز
   */
  setMaxTokens(maxTokens: number): void {
    this.maxTokens = maxTokens;
  }
}

// تصدير نسخة واحدة من الخدمة (Singleton)
export const chatService = new ChatService();
